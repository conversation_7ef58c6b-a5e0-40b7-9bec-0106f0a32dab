import { ipcMain } from 'electron'
import { figmaApiRequest, parseFigmaUrl } from './figma-api'

/**
 * 设置所有 IPC 处理器
 */
export function setupIpcHandlers(): void {
  // Figma API 请求处理器
  ipcMain.handle('figma-api-request', async (event, { url, params, headers }) => {
    return await figmaApiRequest({ url, params, headers })
  })

  // Figma 链接解析处理器
  ipcMain.handle('parse-figma-url', async (event, url: string) => {
    return await parseFigmaUrl(url)
  })
}
